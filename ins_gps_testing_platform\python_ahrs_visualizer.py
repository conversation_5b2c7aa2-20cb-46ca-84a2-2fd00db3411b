import numpy as np
import matplotlib.pyplot as plt
import matplotlib.animation as animation
from matplotlib.patches import Wedge, FancyArrowPatch
from matplotlib.patches import Circle
import pandas as pd
import math

class AHRSVisualizer:
    def __init__(self, filename):
        """Initialize the AHRS visualizer with data from file"""
        self.load_ahrs_data(filename)
        self.setup_plot()
        self.current_index = 0
        
    def load_ahrs_data(self, filename):
        """Load and process AHRS data from file"""
        try:
            # Load AHRS data (assuming tab-separated format)
            data = pd.read_csv(filename, sep='\t', header=None)
            
            # Extract columns: timestamp, qx, qy, qz, qw, gyro_x, gyro_y, gyro_z, acc_x, acc_y, acc_z
            self.timestamps = data.iloc[:, 0].values
            self.quaternions = data.iloc[:, 1:5].values  # qx, qy, qz, qw
            
            # Convert to relative time (start from 0)
            self.rel_time = self.timestamps - self.timestamps[0]
            
            # Extract heading from quaternions
            self.headings = self.quaternions_to_heading(self.quaternions)
            
            # Reference to starting position (make start = 0°)
            self.headings_relative = self.headings - self.headings[0]
            
            # Unwrap angles to prevent discontinuities
            self.headings_unwrapped = np.unwrap(np.radians(self.headings_relative)) * 180 / np.pi
            
            print(f"✅ Loaded {len(self.timestamps)} data points")
            print(f"📊 Duration: {self.rel_time[-1]:.3f} seconds")
            print(f"🔄 Total rotation: {self.headings_unwrapped[-1]:.2f}°")
            print(f"📈 Average rate: {self.headings_unwrapped[-1]/self.rel_time[-1]:.2f}°/s")
            
        except Exception as e:
            print(f"❌ Error loading file: {e}")
            raise
    
    def quaternions_to_heading(self, quaternions):
        """Convert quaternions to heading angles (yaw) in degrees"""
        headings = []
        for q in quaternions:
            qx, qy, qz, qw = q
            
            # Convert quaternion to yaw (heading)
            siny_cosp = 2 * (qw * qz + qx * qy)
            cosy_cosp = 1 - 2 * (qy * qy + qz * qz)
            yaw = np.arctan2(siny_cosp, cosy_cosp) * 180 / np.pi
            
            headings.append(yaw)
        
        return np.array(headings)
    
    def setup_plot(self):
        """Setup the matplotlib plot"""
        self.fig, (self.ax1, self.ax2) = plt.subplots(1, 2, figsize=(15, 7))
        
        # Main visualization (left plot)
        self.ax1.set_xlim(-3, 3)
        self.ax1.set_ylim(-3, 3)
        self.ax1.set_aspect('equal')
        self.ax1.grid(True, alpha=0.3)
        self.ax1.set_title('🧭 AHRS Heading Visualization\n()', fontsize=14, fontweight='bold')
        self.ax1.set_xlabel('Distance (arbitrary units)')
        self.ax1.set_ylabel('Distance (arbitrary units)')
        
        # Add coordinate reference
        self.ax1.axhline(y=0, color='k', linestyle='-', alpha=0.3)
        self.ax1.axvline(x=0, color='k', linestyle='-', alpha=0.3)
        
        # Device position (blue dot) - always at center
        self.device_dot = Circle((0, 0), 0.1, color='#1976d2', zorder=10)
        self.ax1.add_patch(self.device_dot)
        
        # Field of view cone (initially pointing up/north)
        self.fov_angle = 60  # degrees
        self.fov_radius = 2.0
        self.fov_wedge = Wedge((0, 0), self.fov_radius, 
                              -self.fov_angle/2, self.fov_angle/2,
                              facecolor='#1976d2', alpha=0.2, zorder=1)
        self.ax1.add_patch(self.fov_wedge)
        
        # Heading arrow
        self.arrow = FancyArrowPatch((0, 0), (0, 1.5), 
                                   connectionstyle="arc3", 
                                   arrowstyle='->', 
                                   mutation_scale=20, 
                                   color='#1976d2', 
                                   linewidth=3, zorder=5)
        self.ax1.add_patch(self.arrow)
        
        # Add compass directions
        compass_radius = 2.7
        directions = [('N', 0, 90), ('E', 90, 0), ('S', 180, -90), ('W', 270, 180)]
        for label, angle, text_angle in directions:
            x = compass_radius * np.cos(np.radians(text_angle))
            y = compass_radius * np.sin(np.radians(text_angle))
            self.ax1.text(x, y, label, ha='center', va='center', 
                         fontsize=12, fontweight='bold', color='gray')
        
        # Stats text
        self.stats_text = self.ax1.text(-2.8, 2.5, '', fontsize=10, 
                                       bbox=dict(boxstyle="round,pad=0.3", 
                                               facecolor='white', alpha=0.8))
        
        # Heading vs time plot (right plot)
        self.ax2.plot(self.rel_time, self.headings_unwrapped, 'b-', linewidth=2, label='Heading')
        self.ax2.set_xlabel('Time (seconds)')
        self.ax2.set_ylabel('Heading (degrees)')
        self.ax2.set_title('📈 Heading vs Time', fontweight='bold')
        self.ax2.grid(True, alpha=0.3)
        self.ax2.legend()
        
        # Current position marker on time plot
        self.time_marker = self.ax2.plot([], [], 'ro', markersize=8, zorder=10)[0]
        
        plt.tight_layout()
    
    def update_display(self, index):
        """Update the display for given data index"""
        if index >= len(self.headings_unwrapped):
            index = len(self.headings_unwrapped) - 1
        
        current_heading = self.headings_unwrapped[index]
        current_time = self.rel_time[index]
        
        # Convert heading to radians for rotation
        heading_rad = np.radians(current_heading)
        
        # Update field of view wedge
        self.fov_wedge.remove()
        theta1 = current_heading - self.fov_angle/2
        theta2 = current_heading + self.fov_angle/2
        self.fov_wedge = Wedge((0, 0), self.fov_radius, theta1, theta2,
                              facecolor='#1976d2', alpha=0.2, zorder=1)
        self.ax1.add_patch(self.fov_wedge)
        
        # Update heading arrow
        arrow_end_x = 1.5 * np.sin(heading_rad)
        arrow_end_y = 1.5 * np.cos(heading_rad)
        
        self.arrow.remove()
        self.arrow = FancyArrowPatch((0, 0), (arrow_end_x, arrow_end_y),
                                   connectionstyle="arc3",
                                   arrowstyle='->', 
                                   mutation_scale=20,
                                   color='#1976d2',
                                   linewidth=3, zorder=5)
        self.ax1.add_patch(self.arrow)
        
        # Update stats text
        rate = current_heading / current_time if current_time > 0 else 0
        stats_str = f"""📊 Real-time Data:
Time: {current_time:.3f}s
Heading: {current_heading:.2f}°
Rate: {rate:.2f}°/s
Progress: {index}/{len(self.headings_unwrapped)-1}"""
        
        self.stats_text.set_text(stats_str)
        
        # Update time plot marker
        self.time_marker.set_data([current_time], [current_heading])
        
        return [self.fov_wedge, self.arrow, self.stats_text, self.time_marker]
    
    def animate(self, frame):
        """Animation function for matplotlib animation"""
        return self.update_display(frame)
    
    def show_static(self, index=0):
        """Show static display at specific index"""
        self.update_display(index)
        plt.show()
    
    def show_animation(self, interval=1000, repeat=True, skip_frames=1):
        """Show animated visualization"""
        frames = len(self.headings_unwrapped) // skip_frames

        def animate_with_skip(frame):
            return self.animate(frame * skip_frames)

        anim = animation.FuncAnimation(self.fig, animate_with_skip, frames=frames,
                                     interval=interval, blit=False, repeat=repeat)

        plt.show()
        return anim
    
    def show_interactive(self):
        """Show interactive visualization with slider"""
        from matplotlib.widgets import Slider
        
        # Adjust layout for slider
        plt.subplots_adjust(bottom=0.15)
        
        # Add slider
        ax_slider = plt.axes([0.2, 0.05, 0.5, 0.03])
        slider = Slider(ax_slider, 'Time', 0, len(self.headings_unwrapped)-1, 
                       valinit=0, valfmt='%d')
        
        def update_slider(val):
            index = int(slider.val)
            self.update_display(index)
            self.fig.canvas.draw()
        
        slider.on_changed(update_slider)
        
        # Initial display
        self.update_display(0)
        plt.show()
        
        return slider

def main():
    """Main function to run the visualizer"""
    # File path - update this to your AHRS file location
    filename = '..\\pohang_dataset_005\\navigation\\ahrs.txt'  # Change this to your file path
    
    try:
        # Create visualizer
        visualizer = AHRSVisualizer(filename)
        
        print("\n🎯 Choose visualization mode:")
        print("1. Static view (single frame)")
        print("2. Animation (automatic)")
        print("3. Interactive (with slider)")
        
        mode = input("Enter mode (1/2/3): ").strip()
        
        if mode == '1':
            print("📊 Showing static view...")
            visualizer.show_static(index=0)  # Show first frame
            
        elif mode == '2':
            print("🎬 Starting animation...")
            # For large datasets, skip frames for faster visualization
            skip = max(1, len(visualizer.headings_unwrapped) // 5000)  # Target ~5000 frames max
            anim = visualizer.show_animation(interval=1000, skip_frames=skip)  # 20ms between frames
            
        elif mode == '3':
            print("🎛️ Starting interactive mode...")
            slider = visualizer.show_interactive()
            
        else:
            print("⚠️ Invalid mode, showing static view...")
            visualizer.show_static()
            
    except FileNotFoundError:
        print(f"❌ File not found: {filename}")
        print("💡 Make sure the AHRS file is in the same directory or update the filename variable")
    except Exception as e:
        print(f"❌ Error: {e}")

if __name__ == "__main__":
    main()

#!/usr/bin/env python3
"""
GPS Elevation Analysis and Filtering Tool

This script analyzes GPS elevation data from Pohang dataset and provides
interactive filtering capabilities with GUI controls.

Based on Data Structure.md:
GPS Format (11 columns):
| Unix time (s) | GPS time (s) | Latitude (deg) | Hemisphere of latitude (N/S) | 
| Longitude (deg) | Hemisphere of longitude (E/W) | Heading (deg) | GPS quality indicator | 
| Number of satellites | Horizontal dilution of precision | Geoid height (m) |
"""

import numpy as np
import matplotlib.pyplot as plt
from matplotlib.widgets import Slider, Button, CheckButtons
import pandas as pd
from pathlib import Path
import logging
from scipy import signal
from scipy.stats import zscore
import tkinter as tk
from tkinter import filedialog, messagebox, ttk

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class GPSElevationAnalyzer:
    def __init__(self, gps_file_path):
        """Initialize the GPS elevation analyzer"""
        self.gps_file_path = Path(gps_file_path)
        self.original_data = None
        self.filtered_data = None
        self.load_gps_data()
        
        # Filter parameters
        self.filter_params = {
            'remove_outliers': True,
            'outlier_threshold': 3.0,  # Z-score threshold
            'apply_smoothing': True,
            'smoothing_window': 5,
            'remove_spikes': True,
            'spike_threshold': 2.0,  # meters
            'quality_filter': True,
            'min_quality': 1,  # GPS quality indicator
            'satellite_filter': True,
            'min_satellites': 4,
            'hdop_filter': True,
            'max_hdop': 5.0
        }
        
    def load_gps_data(self):
        """Load GPS data from file"""
        try:
            logger.info(f"Loading GPS data from: {self.gps_file_path}")
            
            # Load data (tab-delimited, no header)
            data = pd.read_csv(self.gps_file_path, sep='\t', header=None)
            
            # Parse columns according to Data Structure.md
            self.original_data = {
                'unix_time': data.iloc[:, 0].values,
                'gps_time': data.iloc[:, 1].values,
                'latitude': data.iloc[:, 2].values,
                'lat_hemisphere': data.iloc[:, 3].values,
                'longitude': data.iloc[:, 4].values,
                'lon_hemisphere': data.iloc[:, 5].values,
                'heading': data.iloc[:, 6].values,
                'quality': data.iloc[:, 7].values,
                'num_satellites': data.iloc[:, 8].values,
                'hdop': data.iloc[:, 9].values,
                'elevation': data.iloc[:, 10].values  # Geoid height
            }
            
            # Apply hemisphere corrections
            for i in range(len(self.original_data['latitude'])):
                if self.original_data['lat_hemisphere'][i] == 'S':
                    self.original_data['latitude'][i] *= -1
                if self.original_data['lon_hemisphere'][i] == 'W':
                    self.original_data['longitude'][i] *= -1
            
            # Convert to relative time (start from 0)
            self.original_data['rel_time'] = (
                self.original_data['unix_time'] - self.original_data['unix_time'][0]
            )
            
            logger.info(f"Loaded {len(self.original_data['unix_time'])} GPS data points")
            logger.info(f"Duration: {self.original_data['rel_time'][-1]:.1f} seconds")
            logger.info(f"Elevation range: {np.min(self.original_data['elevation']):.2f} to {np.max(self.original_data['elevation']):.2f} m")
            
            # Initialize filtered data as copy of original
            self.filtered_data = self.original_data.copy()
            
        except Exception as e:
            logger.error(f"Error loading GPS data: {e}")
            raise
    
    def analyze_elevation_statistics(self, data=None):
        """Analyze elevation statistics"""
        if data is None:
            data = self.original_data
            
        elevation = data['elevation']
        
        stats = {
            'count': len(elevation),
            'mean': np.mean(elevation),
            'median': np.median(elevation),
            'std': np.std(elevation),
            'min': np.min(elevation),
            'max': np.max(elevation),
            'range': np.max(elevation) - np.min(elevation),
            'q25': np.percentile(elevation, 25),
            'q75': np.percentile(elevation, 75),
            'iqr': np.percentile(elevation, 75) - np.percentile(elevation, 25)
        }
        
        return stats
    
    def detect_outliers(self, data, threshold=3.0):
        """Detect outliers using Z-score method"""
        elevation = data['elevation']
        z_scores = np.abs(zscore(elevation))
        outlier_mask = z_scores > threshold
        return outlier_mask
    
    def detect_spikes(self, data, threshold=2.0):
        """Detect elevation spikes using gradient analysis"""
        elevation = data['elevation']
        
        # Calculate gradient (rate of change)
        gradient = np.gradient(elevation)
        
        # Find points where gradient exceeds threshold
        spike_mask = np.abs(gradient) > threshold
        
        return spike_mask
    
    def apply_smoothing(self, elevation, window_size=5):
        """Apply moving average smoothing"""
        if window_size < 2:
            return elevation

        # Use pandas rolling mean for smoothing
        df_temp = pd.DataFrame({'elevation': elevation})
        smoothed = df_temp['elevation'].rolling(window=window_size, center=True, min_periods=1).mean().values
        return smoothed
    
    def apply_filters(self):
        """Apply all selected filters to the data"""
        # Start with original data
        filtered = self.original_data.copy()
        
        # Create mask for points to keep
        keep_mask = np.ones(len(filtered['elevation']), dtype=bool)
        
        # Quality filter
        if self.filter_params['quality_filter']:
            quality_mask = filtered['quality'] >= self.filter_params['min_quality']
            keep_mask &= quality_mask
            logger.info(f"Quality filter: keeping {np.sum(quality_mask)}/{len(quality_mask)} points")
        
        # Satellite count filter
        if self.filter_params['satellite_filter']:
            sat_mask = filtered['num_satellites'] >= self.filter_params['min_satellites']
            keep_mask &= sat_mask
            logger.info(f"Satellite filter: keeping {np.sum(sat_mask)}/{len(sat_mask)} points")
        
        # HDOP filter
        if self.filter_params['hdop_filter']:
            hdop_mask = filtered['hdop'] <= self.filter_params['max_hdop']
            keep_mask &= hdop_mask
            logger.info(f"HDOP filter: keeping {np.sum(hdop_mask)}/{len(hdop_mask)} points")
        
        # Apply mask to all data
        for key in filtered.keys():
            filtered[key] = filtered[key][keep_mask]
        
        # Outlier removal
        if self.filter_params['remove_outliers']:
            outlier_mask = self.detect_outliers(filtered, self.filter_params['outlier_threshold'])
            outlier_indices = np.where(outlier_mask)[0]
            logger.info(f"Outlier filter: removing {len(outlier_indices)} outliers")
            
            # Remove outliers
            keep_indices = ~outlier_mask
            for key in filtered.keys():
                filtered[key] = filtered[key][keep_indices]
        
        # Spike removal
        if self.filter_params['remove_spikes']:
            spike_mask = self.detect_spikes(filtered, self.filter_params['spike_threshold'])
            spike_indices = np.where(spike_mask)[0]
            logger.info(f"Spike filter: removing {len(spike_indices)} spikes")
            
            # Remove spikes
            keep_indices = ~spike_mask
            for key in filtered.keys():
                filtered[key] = filtered[key][keep_indices]
        
        # Smoothing (applied to elevation only)
        if self.filter_params['apply_smoothing']:
            filtered['elevation'] = self.apply_smoothing(
                filtered['elevation'], 
                self.filter_params['smoothing_window']
            )
            logger.info(f"Applied smoothing with window size {self.filter_params['smoothing_window']}")
        
        self.filtered_data = filtered
        logger.info(f"Final filtered data: {len(self.filtered_data['elevation'])} points")
        
        return filtered
    
    def plot_elevation_analysis(self):
        """Create comprehensive elevation analysis plots"""
        fig = plt.figure(figsize=(16, 12))
        fig.suptitle('GPS Elevation Analysis and Filtering', fontsize=16, fontweight='bold')
        
        # Original vs filtered elevation time series
        ax1 = plt.subplot(2, 3, 1)
        ax1.plot(self.original_data['rel_time'], self.original_data['elevation'], 
                'b-', alpha=0.7, linewidth=1, label='Original')
        ax1.plot(self.filtered_data['rel_time'], self.filtered_data['elevation'], 
                'r-', linewidth=2, label='Filtered')
        ax1.set_xlabel('Time (s)')
        ax1.set_ylabel('Elevation (m)')
        ax1.set_title('Elevation vs Time')
        ax1.grid(True, alpha=0.3)
        ax1.legend()
        
        # Elevation histogram
        ax2 = plt.subplot(2, 3, 2)
        ax2.hist(self.original_data['elevation'], bins=50, alpha=0.7, 
                label='Original', density=True, color='blue')
        ax2.hist(self.filtered_data['elevation'], bins=50, alpha=0.7, 
                label='Filtered', density=True, color='red')
        ax2.set_xlabel('Elevation (m)')
        ax2.set_ylabel('Density')
        ax2.set_title('Elevation Distribution')
        ax2.grid(True, alpha=0.3)
        ax2.legend()
        
        # Elevation gradient (rate of change)
        ax3 = plt.subplot(2, 3, 3)
        original_gradient = np.gradient(self.original_data['elevation'])
        filtered_gradient = np.gradient(self.filtered_data['elevation'])
        ax3.plot(self.original_data['rel_time'], original_gradient, 
                'b-', alpha=0.7, linewidth=1, label='Original')
        ax3.plot(self.filtered_data['rel_time'], filtered_gradient, 
                'r-', linewidth=2, label='Filtered')
        ax3.set_xlabel('Time (s)')
        ax3.set_ylabel('Elevation Rate (m/s)')
        ax3.set_title('Elevation Rate of Change')
        ax3.grid(True, alpha=0.3)
        ax3.legend()
        
        # GPS quality indicators
        ax4 = plt.subplot(2, 3, 4)
        ax4.scatter(self.original_data['rel_time'], self.original_data['elevation'], 
                   c=self.original_data['quality'], cmap='viridis', alpha=0.6, s=1)
        cbar = plt.colorbar(ax4.scatter([], [], c=[], cmap='viridis'), ax=ax4)
        cbar.set_label('GPS Quality')
        ax4.set_xlabel('Time (s)')
        ax4.set_ylabel('Elevation (m)')
        ax4.set_title('Elevation colored by GPS Quality')
        ax4.grid(True, alpha=0.3)
        
        # Satellite count vs elevation
        ax5 = plt.subplot(2, 3, 5)
        ax5.scatter(self.original_data['num_satellites'], self.original_data['elevation'], 
                   alpha=0.6, s=1, label='Original')
        ax5.scatter(self.filtered_data['num_satellites'], self.filtered_data['elevation'], 
                   alpha=0.8, s=1, color='red', label='Filtered')
        ax5.set_xlabel('Number of Satellites')
        ax5.set_ylabel('Elevation (m)')
        ax5.set_title('Elevation vs Satellite Count')
        ax5.grid(True, alpha=0.3)
        ax5.legend()
        
        # Statistics comparison
        ax6 = plt.subplot(2, 3, 6)
        ax6.axis('off')
        
        orig_stats = self.analyze_elevation_statistics(self.original_data)
        filt_stats = self.analyze_elevation_statistics(self.filtered_data)
        
        stats_text = "Statistics Comparison:\n\n"
        stats_text += f"{'Metric':<12} {'Original':<10} {'Filtered':<10}\n"
        stats_text += "-" * 35 + "\n"
        stats_text += f"{'Count':<12} {orig_stats['count']:<10} {filt_stats['count']:<10}\n"
        stats_text += f"{'Mean':<12} {orig_stats['mean']:<10.2f} {filt_stats['mean']:<10.2f}\n"
        stats_text += f"{'Median':<12} {orig_stats['median']:<10.2f} {filt_stats['median']:<10.2f}\n"
        stats_text += f"{'Std Dev':<12} {orig_stats['std']:<10.2f} {filt_stats['std']:<10.2f}\n"
        stats_text += f"{'Min':<12} {orig_stats['min']:<10.2f} {filt_stats['min']:<10.2f}\n"
        stats_text += f"{'Max':<12} {orig_stats['max']:<10.2f} {filt_stats['max']:<10.2f}\n"
        stats_text += f"{'Range':<12} {orig_stats['range']:<10.2f} {filt_stats['range']:<10.2f}\n"
        
        ax6.text(0.05, 0.95, stats_text, transform=ax6.transAxes, 
                fontfamily='monospace', fontsize=10, verticalalignment='top',
                bbox=dict(boxstyle='round', facecolor='lightgray', alpha=0.8))
        
        plt.tight_layout()
        return fig

    def create_interactive_gui(self):
        """Create interactive GUI for filter configuration"""
        fig, axes = plt.subplots(2, 2, figsize=(16, 10))
        fig.suptitle('GPS Elevation Interactive Filter Configuration', fontsize=16, fontweight='bold')

        # Make room for controls
        plt.subplots_adjust(bottom=0.35, right=0.85)

        # Initial plot
        self.update_plots(axes)

        # Create control panel
        self.create_control_panel(fig)

        plt.show()
        return fig

    def update_plots(self, axes):
        """Update all plots with current filter settings"""
        # Clear axes
        for ax in axes.flat:
            ax.clear()

        # Apply current filters
        self.apply_filters()

        # Plot 1: Elevation time series
        ax1 = axes[0, 0]
        ax1.plot(self.original_data['rel_time'], self.original_data['elevation'],
                'b-', alpha=0.5, linewidth=1, label='Original')
        ax1.plot(self.filtered_data['rel_time'], self.filtered_data['elevation'],
                'r-', linewidth=2, label='Filtered')
        ax1.set_xlabel('Time (s)')
        ax1.set_ylabel('Elevation (m)')
        ax1.set_title('Elevation vs Time')
        ax1.grid(True, alpha=0.3)
        ax1.legend()

        # Plot 2: Elevation histogram
        ax2 = axes[0, 1]
        ax2.hist(self.original_data['elevation'], bins=30, alpha=0.5,
                label='Original', density=True, color='blue')
        ax2.hist(self.filtered_data['elevation'], bins=30, alpha=0.7,
                label='Filtered', density=True, color='red')
        ax2.set_xlabel('Elevation (m)')
        ax2.set_ylabel('Density')
        ax2.set_title('Elevation Distribution')
        ax2.grid(True, alpha=0.3)
        ax2.legend()

        # Plot 3: Quality indicators
        ax3 = axes[1, 0]
        scatter = ax3.scatter(self.original_data['rel_time'], self.original_data['elevation'],
                             c=self.original_data['quality'], cmap='viridis', alpha=0.6, s=2)
        ax3.scatter(self.filtered_data['rel_time'], self.filtered_data['elevation'],
                   color='red', alpha=0.8, s=3, label='Filtered')
        ax3.set_xlabel('Time (s)')
        ax3.set_ylabel('Elevation (m)')
        ax3.set_title('GPS Quality (color) and Filtered Points (red)')
        ax3.grid(True, alpha=0.3)
        ax3.legend()

        # Plot 4: Statistics
        ax4 = axes[1, 1]
        ax4.axis('off')

        orig_stats = self.analyze_elevation_statistics(self.original_data)
        filt_stats = self.analyze_elevation_statistics(self.filtered_data)

        stats_text = f"Filter Results:\n\n"
        stats_text += f"Original points: {orig_stats['count']}\n"
        stats_text += f"Filtered points: {filt_stats['count']}\n"
        stats_text += f"Removed: {orig_stats['count'] - filt_stats['count']}\n"
        stats_text += f"Retention: {100*filt_stats['count']/orig_stats['count']:.1f}%\n\n"
        stats_text += f"Elevation Statistics:\n"
        stats_text += f"Mean: {orig_stats['mean']:.2f} → {filt_stats['mean']:.2f} m\n"
        stats_text += f"Std: {orig_stats['std']:.2f} → {filt_stats['std']:.2f} m\n"
        stats_text += f"Range: {orig_stats['range']:.2f} → {filt_stats['range']:.2f} m\n"

        ax4.text(0.05, 0.95, stats_text, transform=ax4.transAxes,
                fontfamily='monospace', fontsize=11, verticalalignment='top',
                bbox=dict(boxstyle='round', facecolor='lightgreen', alpha=0.8))

        plt.draw()

    def create_control_panel(self, fig):
        """Create control panel with sliders and checkboxes"""

        # Control panel area
        control_height = 0.3

        # Checkboxes for filter enable/disable
        checkbox_ax = plt.axes([0.02, 0.02, 0.15, 0.25])
        checkbox_labels = ['Remove Outliers', 'Apply Smoothing', 'Remove Spikes',
                          'Quality Filter', 'Satellite Filter', 'HDOP Filter']
        checkbox_states = [self.filter_params['remove_outliers'],
                          self.filter_params['apply_smoothing'],
                          self.filter_params['remove_spikes'],
                          self.filter_params['quality_filter'],
                          self.filter_params['satellite_filter'],
                          self.filter_params['hdop_filter']]

        self.checkboxes = CheckButtons(checkbox_ax, checkbox_labels, checkbox_states)

        # Sliders for parameters
        slider_x = 0.2
        slider_width = 0.25
        slider_height = 0.02
        slider_spacing = 0.04

        # Outlier threshold slider
        outlier_ax = plt.axes([slider_x, 0.22, slider_width, slider_height])
        self.outlier_slider = Slider(outlier_ax, 'Outlier Z-score', 1.0, 5.0,
                                    valinit=self.filter_params['outlier_threshold'])

        # Smoothing window slider
        smooth_ax = plt.axes([slider_x, 0.18, slider_width, slider_height])
        self.smooth_slider = Slider(smooth_ax, 'Smooth Window', 1, 20,
                                   valinit=self.filter_params['smoothing_window'], valfmt='%d')

        # Spike threshold slider
        spike_ax = plt.axes([slider_x, 0.14, slider_width, slider_height])
        self.spike_slider = Slider(spike_ax, 'Spike Threshold (m)', 0.1, 5.0,
                                  valinit=self.filter_params['spike_threshold'])

        # Quality slider
        quality_ax = plt.axes([slider_x, 0.10, slider_width, slider_height])
        self.quality_slider = Slider(quality_ax, 'Min GPS Quality', 0, 9,
                                    valinit=self.filter_params['min_quality'], valfmt='%d')

        # Satellite slider
        sat_ax = plt.axes([slider_x, 0.06, slider_width, slider_height])
        self.sat_slider = Slider(sat_ax, 'Min Satellites', 3, 15,
                                valinit=self.filter_params['min_satellites'], valfmt='%d')

        # HDOP slider
        hdop_ax = plt.axes([slider_x, 0.02, slider_width, slider_height])
        self.hdop_slider = Slider(hdop_ax, 'Max HDOP', 1.0, 10.0,
                                 valinit=self.filter_params['max_hdop'])

        # Buttons
        button_x = 0.5
        button_width = 0.1
        button_height = 0.04

        # Update button
        update_ax = plt.axes([button_x, 0.15, button_width, button_height])
        self.update_button = Button(update_ax, 'Update Filters')

        # Reset button
        reset_ax = plt.axes([button_x, 0.10, button_width, button_height])
        self.reset_button = Button(reset_ax, 'Reset Filters')

        # Export button
        export_ax = plt.axes([button_x, 0.05, button_width, button_height])
        self.export_button = Button(export_ax, 'Export Filtered')

        # Connect callbacks
        self.update_button.on_clicked(self.on_update_filters)
        self.reset_button.on_clicked(self.on_reset_filters)
        self.export_button.on_clicked(self.on_export_data)
        self.checkboxes.on_clicked(self.on_checkbox_changed)

        # Store axes reference for updates
        self.plot_axes = fig.axes[:4]  # First 4 axes are the plots

    def on_update_filters(self, event):
        """Update filters when button is clicked"""
        # Update parameters from sliders
        self.filter_params['outlier_threshold'] = self.outlier_slider.val
        self.filter_params['smoothing_window'] = int(self.smooth_slider.val)
        self.filter_params['spike_threshold'] = self.spike_slider.val
        self.filter_params['min_quality'] = int(self.quality_slider.val)
        self.filter_params['min_satellites'] = int(self.sat_slider.val)
        self.filter_params['max_hdop'] = self.hdop_slider.val

        # Update plots
        self.update_plots(np.array(self.plot_axes).reshape(2, 2))

    def on_reset_filters(self, event):
        """Reset all filters to default values"""
        # Reset parameters
        self.filter_params = {
            'remove_outliers': True,
            'outlier_threshold': 3.0,
            'apply_smoothing': True,
            'smoothing_window': 5,
            'remove_spikes': True,
            'spike_threshold': 2.0,
            'quality_filter': True,
            'min_quality': 1,
            'satellite_filter': True,
            'min_satellites': 4,
            'hdop_filter': True,
            'max_hdop': 5.0
        }

        # Reset sliders
        self.outlier_slider.reset()
        self.smooth_slider.reset()
        self.spike_slider.reset()
        self.quality_slider.reset()
        self.sat_slider.reset()
        self.hdop_slider.reset()

        # Update plots
        self.update_plots(np.array(self.plot_axes).reshape(2, 2))

    def on_checkbox_changed(self, label):
        """Handle checkbox changes"""
        checkbox_map = {
            'Remove Outliers': 'remove_outliers',
            'Apply Smoothing': 'apply_smoothing',
            'Remove Spikes': 'remove_spikes',
            'Quality Filter': 'quality_filter',
            'Satellite Filter': 'satellite_filter',
            'HDOP Filter': 'hdop_filter'
        }

        if label in checkbox_map:
            param_name = checkbox_map[label]
            # Toggle the parameter
            self.filter_params[param_name] = not self.filter_params[param_name]

            # Auto-update plots
            self.update_plots(np.array(self.plot_axes).reshape(2, 2))

    def on_export_data(self, event):
        """Export filtered data to new file"""
        try:
            # Create output filename
            input_path = Path(self.gps_file_path)
            output_path = input_path.parent / f"{input_path.stem}_filtered{input_path.suffix}"

            # Prepare data for export (same format as original)
            export_data = []

            for i in range(len(self.filtered_data['unix_time'])):
                row = [
                    self.filtered_data['unix_time'][i],
                    self.filtered_data['gps_time'][i],
                    abs(self.filtered_data['latitude'][i]),  # Always positive
                    'N' if self.filtered_data['latitude'][i] >= 0 else 'S',
                    abs(self.filtered_data['longitude'][i]),  # Always positive
                    'E' if self.filtered_data['longitude'][i] >= 0 else 'W',
                    self.filtered_data['heading'][i],
                    int(self.filtered_data['quality'][i]),
                    int(self.filtered_data['num_satellites'][i]),
                    self.filtered_data['hdop'][i],
                    self.filtered_data['elevation'][i]
                ]
                export_data.append(row)

            # Save to file
            df_export = pd.DataFrame(export_data)
            df_export.to_csv(output_path, sep='\t', header=False, index=False, float_format='%.6f')

            logger.info(f"Filtered GPS data exported to: {output_path}")
            logger.info(f"Exported {len(export_data)} filtered points")

            # Show success message
            plt.figtext(0.7, 0.02, f"✓ Exported to:\n{output_path.name}",
                       bbox=dict(boxstyle='round', facecolor='lightgreen', alpha=0.8),
                       fontsize=10)
            plt.draw()

        except Exception as e:
            logger.error(f"Error exporting data: {e}")
            plt.figtext(0.7, 0.02, f"✗ Export failed:\n{str(e)}",
                       bbox=dict(boxstyle='round', facecolor='lightcoral', alpha=0.8),
                       fontsize=10)
            plt.draw()


def main():
    """Main function to run the GPS elevation analyzer"""

    # Default GPS file path (update this to your GPS file location)
    default_gps_file = 'F:/One_Drive/OneDrive - Université Laval/!Projects/Mitacs/EnvisioningLabs/codes/python/mytoolbox/generator/pohang_dataset_005/navigation/gps.txt'

    print("🛰️ GPS Elevation Analysis and Filtering Tool")
    print("=" * 50)

    # Check if default file exists, otherwise prompt for file
    gps_file_path = Path(default_gps_file)

    if not gps_file_path.exists():
        print(f"Default GPS file not found: {default_gps_file}")
        print("Please select your GPS data file...")

        # Use tkinter file dialog
        root = tk.Tk()
        root.withdraw()  # Hide main window

        gps_file_path = filedialog.askopenfilename(
            title="Select GPS data file (gps.txt)",
            filetypes=[("Text files", "*.txt"), ("All files", "*.*")],
            initialdir=str(Path.cwd())
        )

        if not gps_file_path:
            print("No file selected. Exiting...")
            return

        gps_file_path = Path(gps_file_path)

    try:
        # Create analyzer
        analyzer = GPSElevationAnalyzer(gps_file_path)

        print(f"\n📊 Choose analysis mode:")
        print("1. Static analysis plots")
        print("2. Interactive GUI with filtering")

        mode = input("Enter mode (1/2): ").strip()

        if mode == '1':
            print("📈 Generating static analysis plots...")
            # Apply default filters first
            analyzer.apply_filters()
            # Show static plots
            fig = analyzer.plot_elevation_analysis()
            plt.show()

        elif mode == '2':
            print("🎛️ Starting interactive filtering GUI...")
            # Show interactive GUI
            analyzer.create_interactive_gui()

        else:
            print("⚠️ Invalid mode, showing static analysis...")
            analyzer.apply_filters()
            fig = analyzer.plot_elevation_analysis()
            plt.show()

    except FileNotFoundError:
        print(f"❌ GPS file not found: {gps_file_path}")
        print("💡 Make sure the GPS file exists and the path is correct")
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
